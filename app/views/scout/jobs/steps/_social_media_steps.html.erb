<%# Social Media Category Steps %>
<div data-section-name="social_media" class="hidden">

  <!-- Social Media Platform Step -->
  <div data-step-name="social_media_platform" data-step-id="platform" data-form-wizard-target="step" class="hidden">
    <div class="mb-6">
      <h3 class="text-2xl font-semibold text-stone-900 mb-2">Social Media Platform</h3>
      <p class="text-stone-600">Choose the platform where you need content created.</p>
    </div>
    <div class="mb-6">
      <%= form.label :platform, "Which platform do you need content for?",
          class: "block text-sm font-medium text-stone-700 mb-4" %>
      <div class="mt-3 space-y-3">
        <% Job.platforms.each do |key, value| %>
          <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
            <%= form.radio_button :platform, key,
                class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300" %>
            <span class="ml-3 text-sm text-stone-700"><%= key.humanize %></span>
          </label>
        <% end %>
      </div>
      <span class="error-message hidden text-red-500 text-sm mt-2">Please select a platform</span>
    </div>
    <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
      Please select a platform before continuing.
    </div>
  </div>

  <!-- Social Media Goal Step -->
  <div data-step-name="social_media_goal" data-step-id="goal" data-form-wizard-target="step" class="hidden">
    <div class="mb-6">
      <h3 class="text-2xl font-semibold text-stone-900 mb-2">Social Media Goal</h3>
      <p class="text-stone-600">Define the primary objective for your social media content strategy.</p>
    </div>
    <div class="mb-6">
      <%= form.label :outcome, "What's your main goal for social media content?",
          class: "block text-sm font-medium text-stone-700 mb-4" %>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Build Brand Awareness Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "build_brand",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Build Brand Awareness</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Establish thought leadership and increase brand recognition in your industry
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Generate Leads Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "leads",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Generate Leads</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Attract potential customers and capture their contact information
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Book Calls/Consultations Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "booked_calls",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Book Calls/Consultations</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Drive traffic to your calendar to schedule sales calls or consultations
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Drive Traffic to Website Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "drive_traffic_to_offers",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Drive Traffic to Website</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Direct followers to your website, landing pages, or specific offers
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>

        <!-- Gain Followers Card -->
        <label class="relative flex cursor-pointer rounded-lg border border-stone-300 bg-white p-4 shadow-sm focus:outline-none hover:border-stone-400 transition-colors duration-200">
          <%= form.radio_button :outcome, "followers",
              class: "sr-only",
              data: { action: "change->form-wizard#handleGoalChange" } %>
          <span class="flex flex-1">
            <span class="flex flex-col">
              <span class="block text-sm font-medium text-stone-900">Gain Followers</span>
              <span class="mt-1 flex items-center text-sm text-stone-500">
                Grow your social media audience and increase your reach organically
              </span>
            </span>
          </span>
          <!-- Selected state indicator -->
          <svg class="h-5 w-5 text-[#6100FF] opacity-0 transition-opacity duration-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
          </svg>
          <!-- Focus ring -->
          <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
        </label>
      </div>

      <span class="error-message hidden text-red-500 text-sm mt-2">Please select a goal</span>
    </div>

    <!-- Conditional fields for leads/booked calls -->
    <div id="social-media-goal-details" class="mb-6 hidden">
      <div class="p-4 mb-6 border border-amber-200 rounded-md bg-amber-50">
        <p class="mb-2 text-sm font-medium text-amber-800">Important:</p>
        <p class="text-sm text-amber-700">
          Social media platforms have strict policies about lead generation and sales content.
          Your ghostwriter will need to create content that complies with platform guidelines
          while still achieving your business goals.
        </p>
      </div>

      <%= form.label :social_media_goal_type, "How do you want to generate leads/calls?",
          class: "block text-sm font-medium text-stone-700 mb-4" %>
      <div class="mt-3 space-y-3">
        <% Job.social_media_goal_types.each do |key, value| %>
          <label class="inline-flex items-center p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
            <%= form.radio_button :social_media_goal_type, key,
                class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300",
                data: { goal_dependent_required: true } %>
            <span class="ml-3 text-sm text-stone-700">
              <%= case key
                  when 'social_media_leads' then 'Direct lead generation through social media'
                  when 'booked_calls' then 'Drive traffic to booking page/calendar'
                  else key.humanize
                  end %>
            </span>
          </label>
        <% end %>
      </div>
      <span class="error-message hidden text-red-500 text-sm mt-2">Please select how you want to generate leads</span>

      <div class="mt-6">
        <label class="inline-flex items-start p-3 border border-stone-200 rounded-md hover:bg-stone-50 cursor-pointer">
          <%= form.check_box :social_media_understands_risk_acknowledged,
              class: "focus:ring-stone-500 h-4 w-4 text-stone-600 border-stone-300 mt-0.5",
              data: { goal_dependent_required: true } %>
          <span class="ml-3 text-sm text-stone-700">
            I understand that social media lead generation requires careful compliance with platform policies
          </span>
        </label>
        <span class="error-message hidden text-red-500 text-sm mt-2">Please acknowledge the platform policy requirements</span>
      </div>
    </div>

    <div class="step-error-message hidden mt-6 p-4 text-red-700 bg-red-50 border border-red-200 rounded-md text-sm">
      Please complete all required fields before continuing.
    </div>
  </div>

</div>


